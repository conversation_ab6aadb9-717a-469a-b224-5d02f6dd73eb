<?php
namespace common\libs;

use Exception;

class ImapMailClient
{
    private $mailbox;
    private $username;
    private $password;
    private $inbox;

    public function __construct($host, $username, $password, $port = 993, $ssl = true)
    {
        $this->username = $username;
        $this->password = $password;

        $flag          = $ssl ? '/imap/ssl/novalidate-cert' : '/imap/notls';
        $this->mailbox = "{" . $host . ":" . $port . "$flag}INBOX";

        $this->inbox = @imap_open($this->mailbox, $this->username, $this->password);
        if (!$this->inbox) {
            throw new Exception("无法连接邮箱：" . imap_last_error());
        }
    }

    public function __destruct()
    {
        if ($this->inbox) {
            imap_close($this->inbox);
        }
    }

    public function getEmails($criteria = 'ALL')
    {
        $emails = imap_search($this->inbox, $criteria, SE_UID);
        if (!$emails) {
            return [];
        }

        rsort($emails);
        $results = [];
        foreach ($emails as $uid) {
            $overview  = imap_fetch_overview($this->inbox, $uid, FT_UID)[0];
            $results[] = [
                'uid'     => $uid,
                'subject' => $this->decodeMimeStr($overview->subject ?? ''),
                'from'    => $this->decodeMimeStr($overview->from ?? ''),
                'to'      => $this->decodeMimeStr($overview->to ?? ''),
                'date'    => $overview->date ?? '',
                'body'    => $this->getBody($uid),
                'seen'    => isset($overview->seen) && $overview->seen ? true : false,
            ];
        }

        return $results;
    }

    private function getBody($uid)
    {
        $structure = imap_fetchstructure($this->inbox, $uid, FT_UID);
        $body      = '';

        if ($structure && isset($structure->parts)) {
            foreach ($structure->parts as $partNum => $part) {
                if ($part->type == 0) {
                    $bodyPart = imap_fetchbody($this->inbox, $uid, $partNum + 1, FT_UID);
                    $body     .= $this->decodeBody($bodyPart, $part->encoding, $part->parameters ?? []);
                    break;
                }
            }
        } else {
            $bodyPart = imap_fetchbody($this->inbox, $uid, 1, FT_UID);
            $body     .= $this->decodeBody($bodyPart, $structure->encoding ?? 0, $structure->parameters ?? []);
        }

        return $body;
    }

    private function decodeBody($body, $encoding, $parameters = [])
    {
        switch ((int)$encoding) {
            case 3:
                $body = base64_decode($body);
                break;
            case 4:
                $body = quoted_printable_decode($body);
                break;
        }

        $charset = 'UTF-8';
        foreach ($parameters as $param) {
            if (strtolower($param->attribute ?? '') === 'charset') {
                $charset = strtoupper($param->value);
                break;
            }
        }

        if ($charset !== 'UTF-8') {
            $body = @mb_convert_encoding($body, 'UTF-8', $charset);
        }

        return $body;
    }

    private function decodeMimeStr($str)
    {
        $elements = imap_mime_header_decode($str);
        $result   = '';
        foreach ($elements as $el) {
            $charset = strtoupper($el->charset);
            $text    = $el->text;
            if ($charset !== 'DEFAULT' && $charset !== 'UTF-8') {
                $text = @mb_convert_encoding($text, 'UTF-8', $charset);
            }
            $result .= $text;
        }

        return $result;
    }

    /**
     * 标记邮件为已读
     */
    public function markAsRead($uid)
    {
        imap_setflag_full($this->inbox, $uid, "\\Seen", ST_UID);
    }

    /**
     * 删除邮件
     */
    public function deleteEmail($uid)
    {
        imap_delete($this->inbox, $uid, FT_UID);
        imap_expunge($this->inbox);
    }

    /**
     * 下载附件到指定目录
     */
    public function downloadAttachments($uid, $savePath = './attachments')
    {
        $structure   = imap_fetchstructure($this->inbox, $uid, FT_UID);
        $attachments = [];

        if (isset($structure->parts) && count($structure->parts)) {
            foreach ($structure->parts as $i => $part) {
                $isAttachment = false;
                $filename     = '';

                if (isset($part->disposition) && strtolower($part->disposition) === 'attachment') {
                    $isAttachment = true;
                }

                if (isset($part->dparameters)) {
                    foreach ($part->dparameters as $obj) {
                        if (strtolower($obj->attribute) === 'filename') {
                            $filename     = $this->decodeMimeStr($obj->value);
                            $isAttachment = true;
                            break;
                        }
                    }
                }

                if ($isAttachment && $filename) {
                    $body = imap_fetchbody($this->inbox, $uid, $i + 1, FT_UID);
                    if ($part->encoding == 3) {
                        $body = base64_decode($body);
                    } elseif ($part->encoding == 4) {
                        $body = quoted_printable_decode($body);
                    }

                    if (!is_dir($savePath)) {
                        mkdir($savePath, 0777, true);
                    }

                    $filepath = rtrim($savePath, '/') . '/' . $filename;
                    file_put_contents($filepath, $body);
                    $attachments[] = $filepath;
                }
            }
        }

        return $attachments;
    }

    /**
     * 自定义保存逻辑（比如写入数据库）
     * 回调参数：每封邮件的数组结构
     */
    public function saveToDatabase(callable $callback, $criteria = 'ALL')
    {
        $emails = $this->getEmails($criteria);
        foreach ($emails as $mail) {
            $callback($mail);
        }
    }
}